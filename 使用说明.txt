AION XML字符串搜索工具 - 使用说明
=====================================

这个工具可以帮助您通过中文字符串快速定位到AION游戏的XML文件。

【文件准备】
1. 确保您的工作目录包含以下文件：
   - strings文件夹（包含字符串文件）
   - client_items_armor.xml（装备文件）
   - client_items_etc.xml（其他物品文件）
   - client_items_misc.xml（杂项物品文件）
   - client_npcs.xml（NPC文件）
   - client_skills.xml（技能文件）
   - 等其他XML文件

2. 将XmlStringSearcher.exe复制到包含这些文件的目录中

【使用步骤】
1. 双击运行XmlStringSearcher.exe
2. 点击"加载字符串文件"按钮，加载strings文件夹中的中文字符串
3. 点击"加载XML文件"按钮，加载所有XML文件的内容
4. 在搜索框中输入中文字符串，程序会自动搜索匹配的项目
5. 双击搜索结果可以用记事本打开对应的XML文件
6. 点击"导出搜索结果"可以将结果保存为CSV文件

【搜索功能】
- 支持名称直接匹配
- 支持描述直接匹配  
- 支持名称中文匹配
- 支持描述中文匹配

【支持的XML文件】
- client_combine_recipe.xml（合成配方）
- client_items_armor.xml（装备）
- client_items_etc.xml（其他物品）
- client_items_misc.xml（杂项物品）
- client_npc_goodslist.xml（NPC商品列表）
- client_npc_purchase_list.xml（NPC购买列表）
- client_npc_trade_in_list.xml（NPC交易列表）
- client_npcs.xml（NPC信息）
- client_skill_prohibit.xml（技能限制）
- client_skills.xml（技能信息）
- client_titles.xml（称号信息）

【注意事项】
- 首次使用需要先加载字符串文件和XML文件
- 搜索是实时的，输入文字时会自动搜索
- 如果搜索结果太多，可以输入更具体的关键词
- 程序会自动识别工作目录，显示在界面顶部

【故障排除】
- 如果无法加载字符串文件，请检查strings文件夹是否存在
- 如果无法加载XML文件，请检查文件是否存在且格式正确
- 如果搜索无结果，请确保已正确加载字符串文件和XML文件

开发信息：基于C# Windows Forms开发，支持.NET Framework 4.0+
