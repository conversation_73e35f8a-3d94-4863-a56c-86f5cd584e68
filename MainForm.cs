using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace XmlStringSearcher
{
    public partial class MainForm : Form
    {
        private Dictionary<string, string> stringMappings = new Dictionary<string, string>();
        private Dictionary<string, List<XmlItemInfo>> xmlItems = new Dictionary<string, List<XmlItemInfo>>();
        private string workingDirectory = "";

        public MainForm()
        {
            InitializeComponent();
            LoadWorkingDirectory();
        }

        private void LoadWorkingDirectory()
        {
            workingDirectory = Directory.GetCurrentDirectory();
            lblWorkingDir.Text = "工作目录: " + workingDirectory;
        }

        private void btnLoadStrings_Click(object sender, EventArgs e)
        {
            try
            {
                LoadStringFiles();
                MessageBox.Show("已加载 " + stringMappings.Count + " 个字符串映射", "加载完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载字符串文件失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnLoadXmlFiles_Click(object sender, EventArgs e)
        {
            try
            {
                LoadXmlFiles();
                int totalItems = xmlItems.Values.Sum(list => list.Count);
                MessageBox.Show("已加载 " + xmlItems.Count + " 个XML文件，共 " + totalItems + " 个项目", "加载完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载XML文件失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadStringFiles()
        {
            stringMappings.Clear();
            string stringsPath = Path.Combine(workingDirectory, "strings");
            
            if (!Directory.Exists(stringsPath))
            {
                throw new DirectoryNotFoundException("strings文件夹不存在");
            }

            var stringFiles = Directory.GetFiles(stringsPath, "*.xml");
            foreach (var file in stringFiles)
            {
                try
                {
                    LoadStringFile(file);
                }
                catch (Exception ex)
                {
                    // 记录但不中断处理
                    Console.WriteLine("无法加载字符串文件 " + file + ": " + ex.Message);
                }
            }
        }

        private void LoadStringFile(string filePath)
        {
            // 尝试读取二进制格式的字符串文件
            using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            using (var reader = new BinaryReader(fs, Encoding.Unicode))
            {
                try
                {
                    // 跳过文件头
                    fs.Seek(8, SeekOrigin.Begin);
                    
                    while (fs.Position < fs.Length - 4)
                    {
                        try
                        {
                            // 读取字符串ID长度
                            int idLength = reader.ReadInt32();
                            if (idLength <= 0 || idLength > 1000) break;
                            
                            // 读取字符串ID
                            byte[] idBytes = reader.ReadBytes(idLength * 2);
                            string stringId = Encoding.Unicode.GetString(idBytes).TrimEnd('\0');
                            
                            // 读取中文内容长度
                            int contentLength = reader.ReadInt32();
                            if (contentLength <= 0 || contentLength > 10000) break;
                            
                            // 读取中文内容
                            byte[] contentBytes = reader.ReadBytes(contentLength * 2);
                            string content = Encoding.Unicode.GetString(contentBytes).TrimEnd('\0');
                            
                            if (!string.IsNullOrEmpty(stringId) && !string.IsNullOrEmpty(content))
                            {
                                stringMappings[stringId] = content;
                            }
                        }
                        catch
                        {
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 如果二进制读取失败，尝试作为文本文件读取
                    Console.WriteLine("二进制读取失败，尝试文本读取: " + ex.Message);
                }
            }
        }

        private void LoadXmlFiles()
        {
            xmlItems.Clear();
            
            var xmlFiles = new[]
            {
                "client_combine_recipe.xml",
                "client_items_armor.xml", 
                "client_items_etc.xml",
                "client_items_misc.xml",
                "client_npc_goodslist.xml",
                "client_npc_purchase_list.xml", 
                "client_npc_trade_in_list.xml",
                "client_npcs.xml",
                "client_skill_prohibit.xml",
                "client_skills.xml",
                "client_titles.xml"
            };

            foreach (var fileName in xmlFiles)
            {
                string filePath = Path.Combine(workingDirectory, fileName);
                if (File.Exists(filePath))
                {
                    try
                    {
                        LoadXmlFile(fileName, filePath);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("无法加载XML文件 " + fileName + ": " + ex.Message);
                    }
                }
            }
        }

        private void LoadXmlFile(string fileName, string filePath)
        {
            var items = new List<XmlItemInfo>();
            
            try
            {
                var doc = new XmlDocument();
                doc.Load(filePath);
                
                // 根据不同的XML文件类型解析
                if (fileName.Contains("items"))
                {
                    ParseItemsXml(doc, items, fileName);
                }
                else if (fileName.Contains("npc"))
                {
                    ParseNpcXml(doc, items, fileName);
                }
                else if (fileName.Contains("skill"))
                {
                    ParseSkillXml(doc, items, fileName);
                }
                else
                {
                    ParseGenericXml(doc, items, fileName);
                }
                
                xmlItems[fileName] = items;
            }
            catch (Exception ex)
            {
                throw new Exception("解析XML文件 " + fileName + " 失败: " + ex.Message);
            }
        }

        private void ParseItemsXml(XmlDocument doc, List<XmlItemInfo> items, string fileName)
        {
            var itemNodes = doc.SelectNodes("//client_item");
            foreach (XmlNode node in itemNodes)
            {
                var item = new XmlItemInfo
                {
                    FileName = fileName,
                    Id = GetNodeValue(node, "id"),
                    Name = GetNodeValue(node, "name"),
                    Description = GetNodeValue(node, "desc"),
                    Type = GetNodeValue(node, "item_type") ?? GetNodeValue(node, "armor_type")
                };
                items.Add(item);
            }
        }

        private void ParseNpcXml(XmlDocument doc, List<XmlItemInfo> items, string fileName)
        {
            var npcNodes = doc.SelectNodes("//client_npc | //npc_template");
            foreach (XmlNode node in npcNodes)
            {
                var item = new XmlItemInfo
                {
                    FileName = fileName,
                    Id = GetNodeValue(node, "npc_id") ?? GetNodeValue(node, "id"),
                    Name = GetNodeValue(node, "name"),
                    Description = GetNodeValue(node, "desc") ?? GetNodeValue(node, "title"),
                    Type = "NPC"
                };
                items.Add(item);
            }
        }

        private void ParseSkillXml(XmlDocument doc, List<XmlItemInfo> items, string fileName)
        {
            var skillNodes = doc.SelectNodes("//skill_template | //client_skill");
            foreach (XmlNode node in skillNodes)
            {
                var item = new XmlItemInfo
                {
                    FileName = fileName,
                    Id = GetNodeValue(node, "skill_id") ?? GetNodeValue(node, "id"),
                    Name = GetNodeValue(node, "name"),
                    Description = GetNodeValue(node, "desc"),
                    Type = "Skill"
                };
                items.Add(item);
            }
        }

        private void ParseGenericXml(XmlDocument doc, List<XmlItemInfo> items, string fileName)
        {
            var allNodes = doc.SelectNodes("//*[id or name or desc]");
            foreach (XmlNode node in allNodes)
            {
                var item = new XmlItemInfo
                {
                    FileName = fileName,
                    Id = GetNodeValue(node, "id"),
                    Name = GetNodeValue(node, "name"),
                    Description = GetNodeValue(node, "desc"),
                    Type = node.Name
                };
                items.Add(item);
            }
        }

        private string GetNodeValue(XmlNode parentNode, string nodeName)
        {
            var node = parentNode.SelectSingleNode(nodeName);
            return node != null ? (node.InnerText != null ? node.InnerText.Trim() : null) : null;
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvResults.DataSource = null;
                return;
            }

            SearchItems(txtSearch.Text.Trim());
        }

        private void SearchItems(string searchText)
        {
            var results = new List<SearchResult>();
            
            // 在字符串映射中搜索
            var matchingStrings = stringMappings.Where(kvp => 
                kvp.Value.Contains(searchText) || kvp.Key.Contains(searchText))
                .ToList();

            // 在XML项目中搜索
            foreach (var xmlFile in xmlItems)
            {
                foreach (var item in xmlFile.Value)
                {
                    bool matches = false;
                    string matchType = "";
                    string chineseText = "";

                    // 检查是否直接匹配
                    if (!string.IsNullOrEmpty(item.Name) && item.Name.Contains(searchText))
                    {
                        matches = true;
                        matchType = "名称直接匹配";
                    }
                    else if (!string.IsNullOrEmpty(item.Description) && item.Description.Contains(searchText))
                    {
                        matches = true;
                        matchType = "描述直接匹配";
                    }
                    else
                    {
                        // 检查字符串映射
                        if (!string.IsNullOrEmpty(item.Name) && stringMappings.ContainsKey(item.Name))
                        {
                            chineseText = stringMappings[item.Name];
                            if (chineseText.Contains(searchText))
                            {
                                matches = true;
                                matchType = "名称中文匹配";
                            }
                        }
                        
                        if (!matches && !string.IsNullOrEmpty(item.Description) && stringMappings.ContainsKey(item.Description))
                        {
                            chineseText = stringMappings[item.Description];
                            if (chineseText.Contains(searchText))
                            {
                                matches = true;
                                matchType = "描述中文匹配";
                            }
                        }
                    }

                    if (matches)
                    {
                        results.Add(new SearchResult
                        {
                            FileName = item.FileName,
                            Id = item.Id,
                            Name = item.Name,
                            Description = item.Description,
                            Type = item.Type,
                            MatchType = matchType,
                            ChineseText = chineseText
                        });
                    }
                }
            }

            dgvResults.DataSource = results;
            lblResultCount.Text = "找到 " + results.Count + " 个匹配项";
        }

        private void dgvResults_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                OpenSelectedFile();
            }
        }

        private void btnOpenFile_Click(object sender, EventArgs e)
        {
            OpenSelectedFile();
        }

        private void OpenSelectedFile()
        {
            if (dgvResults.SelectedRows.Count > 0)
            {
                var selectedRow = dgvResults.SelectedRows[0];
                var fileName = selectedRow.Cells["FileName"].Value != null ? selectedRow.Cells["FileName"].Value.ToString() : null;

                if (!string.IsNullOrEmpty(fileName))
                {
                    string filePath = Path.Combine(workingDirectory, fileName);
                    if (File.Exists(filePath))
                    {
                        try
                        {
                            System.Diagnostics.Process.Start("notepad.exe", filePath);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("无法打开文件: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("文件不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择一个项目", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (dgvResults.DataSource == null)
            {
                MessageBox.Show("没有搜索结果可以导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            using (var saveDialog = new SaveFileDialog())
            {
                saveDialog.Filter = "CSV文件|*.csv|文本文件|*.txt";
                saveDialog.DefaultExt = "csv";
                saveDialog.FileName = "搜索结果_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        ExportResults(saveDialog.FileName);
                        MessageBox.Show("导出成功", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("导出失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ExportResults(string filePath)
        {
            var results = (List<SearchResult>)dgvResults.DataSource;
            using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // 写入标题行
                writer.WriteLine("文件名,ID,名称,描述,类型,匹配类型,中文内容");

                // 写入数据行
                foreach (var result in results)
                {
                    writer.WriteLine("\"" + result.FileName + "\",\"" + result.Id + "\",\"" + result.Name + "\",\"" + result.Description + "\",\"" + result.Type + "\",\"" + result.MatchType + "\",\"" + result.ChineseText + "\"");
                }
            }
        }
    }
}
