@echo off
echo 正在编译AION XML字符串搜索工具...

REM 查找MSBuild路径
set MSBUILD_PATH=""
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo 未找到MSBuild，尝试使用.NET Framework编译器...
    goto DOTNET_COMPILE
)

echo 使用MSBuild编译...
%MSBUILD_PATH% XmlStringSearcher.csproj /p:Configuration=Release
if %ERRORLEVEL% EQU 0 (
    echo 编译成功！可执行文件位于: bin\Release\XmlStringSearcher.exe
    goto END
) else (
    echo MSBuild编译失败，尝试使用.NET Framework编译器...
    goto DOTNET_COMPILE
)

:DOTNET_COMPILE
echo 使用.NET Framework编译器...
csc /target:winexe /out:XmlStringSearcher.exe /reference:System.dll,System.Windows.Forms.dll,System.Drawing.dll,System.Xml.dll *.cs
if %ERRORLEVEL% EQU 0 (
    echo 编译成功！可执行文件: XmlStringSearcher.exe
) else (
    echo 编译失败！请检查是否安装了.NET Framework SDK
)

:END
pause
