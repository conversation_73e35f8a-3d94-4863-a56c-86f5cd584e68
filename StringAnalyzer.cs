using System;
using System.IO;
using System.Text;

class StringAnalyzer
{
    static void Main(string[] args)
    {
        string filePath = @"strings\client_strings_item.xml";
        
        if (!File.Exists(filePath))
        {
            Console.WriteLine("文件不存在: " + filePath);
            return;
        }
        
        Console.WriteLine("分析文件: " + filePath);
        Console.WriteLine("文件大小: " + new FileInfo(filePath).Length + " 字节");
        Console.WriteLine();
        
        using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
        {
            // 读取前100个字节并显示
            byte[] buffer = new byte[Math.Min(200, (int)fs.Length)];
            fs.Read(buffer, 0, buffer.Length);
            
            Console.WriteLine("前" + buffer.Length + "字节的十六进制:");
            for (int i = 0; i < buffer.Length; i += 16)
            {
                Console.Write(i.ToString("X8") + ": ");
                for (int j = 0; j < 16 && i + j < buffer.Length; j++)
                {
                    Console.Write(buffer[i + j].ToString("X2") + " ");
                }
                Console.Write(" ");
                for (int j = 0; j < 16 && i + j < buffer.Length; j++)
                {
                    char c = (char)buffer[i + j];
                    Console.Write(char.IsControl(c) ? '.' : c);
                }
                Console.WriteLine();
            }
            
            Console.WriteLine();
            Console.WriteLine("尝试解析为Unicode字符串:");
            
            // 尝试从不同位置开始解析Unicode字符串
            fs.Seek(4, SeekOrigin.Begin);
            using (var reader = new BinaryReader(fs, Encoding.Unicode))
            {
                int count = 0;
                while (fs.Position < fs.Length - 2 && count < 20)
                {
                    try
                    {
                        string str = ReadUnicodeString(reader);
                        if (!string.IsNullOrEmpty(str))
                        {
                            Console.WriteLine("字符串 " + (count + 1) + ": '" + str + "'");
                            count++;
                        }
                        else
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("读取错误: " + ex.Message);
                        break;
                    }
                }
            }
        }
        
        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
    
    static string ReadUnicodeString(BinaryReader reader)
    {
        var bytes = new System.Collections.Generic.List<byte>();
        while (reader.BaseStream.Position < reader.BaseStream.Length - 1)
        {
            byte b1 = reader.ReadByte();
            byte b2 = reader.ReadByte();
            
            // 检查是否是null终止符
            if (b1 == 0 && b2 == 0)
            {
                break;
            }
            
            bytes.Add(b1);
            bytes.Add(b2);
        }
        
        if (bytes.Count > 0)
        {
            return Encoding.Unicode.GetString(bytes.ToArray()).Trim();
        }
        
        return string.Empty;
    }
}
