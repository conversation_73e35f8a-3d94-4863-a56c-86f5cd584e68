# AION XML字符串搜索工具

这是一个专门为AION游戏设计的XML文件和字符串搜索工具，可以帮助您通过中文字符串快速定位到相关的XML文件。

## 功能特性

- **字符串文件解析**: 自动解析strings文件夹中的二进制字符串文件
- **XML文件解析**: 支持解析多种AION XML文件格式
- **中文搜索**: 通过中文字符串快速查找相关项目
- **文件定位**: 双击搜索结果可直接打开对应的XML文件
- **结果导出**: 支持将搜索结果导出为CSV文件

## 支持的XML文件

- client_combine_recipe.xml (合成配方)
- client_items_armor.xml (装备)
- client_items_etc.xml (其他物品)
- client_items_misc.xml (杂项物品)
- client_npc_goodslist.xml (NPC商品列表)
- client_npc_purchase_list.xml (NPC购买列表)
- client_npc_trade_in_list.xml (NPC交易列表)
- client_npcs.xml (NPC信息)
- client_skill_prohibit.xml (技能限制)
- client_skills.xml (技能信息)
- client_titles.xml (称号信息)

## 使用方法

### 1. 编译程序

运行 `build.bat` 文件来编译程序：

```batch
build.bat
```

### 2. 准备文件

确保您的工作目录包含以下结构：
```
工作目录/
├── strings/                    # 字符串文件夹
│   ├── client_strings_item.xml
│   ├── client_strings_npc.xml
│   └── ...
├── client_items_armor.xml      # XML文件
├── client_items_etc.xml
├── client_npcs.xml
└── ...
```

### 3. 运行程序

1. 将编译好的 `XmlStringSearcher.exe` 复制到包含XML文件的目录
2. 双击运行程序
3. 点击"加载字符串文件"按钮加载strings文件夹中的字符串映射
4. 点击"加载XML文件"按钮加载XML文件内容
5. 在搜索框中输入中文字符串进行搜索

### 4. 搜索功能

- **实时搜索**: 在搜索框中输入文字时会自动搜索
- **多种匹配**: 支持名称直接匹配、描述直接匹配、名称中文匹配、描述中文匹配
- **结果显示**: 搜索结果显示文件名、ID、名称、描述、类型、匹配类型和中文内容

### 5. 其他功能

- **打开文件**: 双击搜索结果或点击"打开选中文件"按钮可用记事本打开对应的XML文件
- **导出结果**: 点击"导出搜索结果"按钮可将当前搜索结果保存为CSV文件

## 技术说明

### 字符串文件格式

程序支持解析AION特有的二进制字符串文件格式：
- 文件头: 8字节
- 字符串记录: ID长度(4字节) + ID内容(Unicode) + 内容长度(4字节) + 内容(Unicode)

### XML文件解析

程序根据不同的XML文件类型采用不同的解析策略：
- **物品文件**: 解析 `<client_item>` 节点
- **NPC文件**: 解析 `<client_npc>` 和 `<npc_template>` 节点
- **技能文件**: 解析 `<skill_template>` 和 `<client_skill>` 节点
- **通用文件**: 解析包含id、name、desc属性的所有节点

## 系统要求

- Windows操作系统
- .NET Framework 4.7.2或更高版本
- 足够的内存来加载大型XML文件

## 故障排除

1. **编译失败**: 确保安装了Visual Studio或.NET Framework SDK
2. **字符串加载失败**: 检查strings文件夹是否存在且包含正确的文件
3. **XML加载失败**: 确保XML文件格式正确且编码为UTF-8
4. **搜索无结果**: 确保已正确加载字符串文件和XML文件

## 开发信息

- 开发语言: C#
- 界面框架: Windows Forms
- 目标框架: .NET Framework 4.7.2

## 许可证

本工具仅供学习和研究使用。
